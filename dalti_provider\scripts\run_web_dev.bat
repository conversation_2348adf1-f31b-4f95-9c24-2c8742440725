@echo off
echo Starting Chrome with CORS disabled for development...
echo WARNING: This is for development only!
echo.

REM Kill any existing Chrome processes
taskkill /f /im chrome.exe 2>nul

REM Start Chrome with CORS disabled
start "" "C:\Program Files\Google\Chrome\Application\chrome.exe" ^
  --user-data-dir="C:\temp\chrome-dev-session" ^
  --disable-web-security ^
  --disable-features=VizDisplayCompositor ^
  --allow-running-insecure-content ^
  --disable-site-isolation-trials ^
  http://localhost:54719

echo Chrome started with CORS disabled
echo You can now test your Flutter web app
pause
